
from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Sum, Count, F, Value, DecimalField
from django.db import models
from django.utils import timezone
from datetime import datetime, timedelta
from django.db.models.functions import Coalesce

from .models import (
    ProductCategory, Product, PaymentTerm, SalesOrder, SalesOrderLineItem,
    GoodsDeliveryNote, GoodsDeliveryNoteLineItem, CustomerInvoice, CustomerInvoiceLineItem,
    GoodsDeliveryReturnNote, GoodsDeliveryReturnNoteLineItem, CustomerBill, CustomerBillItem
)
from contacts.models import Contact
from .serializers import (
    # CustomerSerializer moved to contacts app
    ProductCategorySerializer, ProductSerializer,
    PaymentTermSerializer, ProductPricingSerializer,
    SalesOrderSerializer, SalesOrderLineItemSerializer,
    GoodsDeliveryNoteSerializer, GoodsDeliveryNoteLineItemSerializer,
    CustomerInvoiceSerializer, CustomerInvoiceLineItemSerializer,
    GoodsDeliveryReturnNoteSerializer, GoodsDeliveryReturnNoteLineItemSerializer,
    CustomerBillSerializer, CustomerBillItemSerializer
)
from gl.models import Account
from inventory.models import Inventory


# DEPRECATED: CustomerViewSet moved to contacts app
# Use /api/contacts/customers/ instead of /api/sales/customers/
# This class is commented out to avoid conflicts with the proper implementation in contacts.views

"""
DEPRECATED CustomerViewSet - moved to contacts app

class CustomerViewSet(viewsets.ModelViewSet):
    ViewSet for managing customers - MOVED TO CONTACTS APP
    queryset = Contact.objects.all()
    serializer_class = CustomerSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'customer_type', 'taxable']
    search_fields = ['display_name', 'company_name', 'email', 'phone', 'gstin']
    ordering_fields = ['display_name', 'created_at', 'current_balance']
    ordering = ['display_name']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by balance
        min_balance = self.request.query_params.get('min_balance')
        max_balance = self.request.query_params.get('max_balance')

        if min_balance is not None:
            queryset = queryset.filter(current_balance__gte=min_balance)
        if max_balance is not None:
            queryset = queryset.filter(current_balance__lte=max_balance)

        return queryset

    @action(detail=False, methods=['get'])
    def stats(self, request):
        Get customer statistics
        total_customers = self.get_queryset().count()
        active_customers = self.get_queryset().filter(status='active').count()
        inactive_customers = self.get_queryset().filter(status='inactive').count()

        # Calculate total receivables
        total_receivables = self.get_queryset().aggregate(
            total=Sum('current_balance')
        )['total'] or 0

        # Get recent customers (last 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        new_customers = self.get_queryset().filter(
            created_at__gte=thirty_days_ago
        ).count()

        return Response({
            'total_customers': total_customers,
            'active_customers': active_customers,
            'inactive_customers': inactive_customers,
            'total_receivables': float(total_receivables),
            'new_customers_30_days': new_customers,
        })

    @action(detail=True, methods=['get'])
    def invoices(self, request, pk=None):
        Get customer's invoices
        customer = self.get_object()
        invoices = customer.invoices.all().order_by('-created_at')
        serializer = InvoiceSerializer(invoices, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def payments(self, request, pk=None):
        Get customer's payments
        customer = self.get_object()
        payments = customer.payments.all().order_by('-payment_date')
        serializer = PaymentSerializer(payments, many=True)
        return Response(serializer.data)
"""


class ProductCategoryViewSet(viewsets.ModelViewSet):
    """Enhanced ViewSet for managing product categories"""
    queryset = ProductCategory.objects.all()
    serializer_class = ProductCategorySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['division_type', 'is_active', 'parent_category', 'level']
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'code', 'sort_order', 'created_at']
    ordering = ['sort_order', 'name']

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by division type
        division_type = self.request.query_params.get('division_type')
        if division_type:
            queryset = queryset.filter(division_type=division_type)
        
        # Filter by hierarchy level
        level = self.request.query_params.get('level')
        if level:
            queryset = queryset.filter(level=level)
        
        # Filter top-level categories only
        top_level_only = self.request.query_params.get('top_level_only')
        if top_level_only == 'true':
            queryset = queryset.filter(parent_category__isnull=True)
        
        return queryset

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get category statistics"""
        total_categories = self.get_queryset().count()
        active_categories = self.get_queryset().filter(is_active=True).count()
        
        # Count by division type
        division_stats = {}
        for division_type, _ in ProductCategory.DIVISION_TYPE_CHOICES:
            count = self.get_queryset().filter(division_type=division_type, is_active=True).count()
            division_stats[division_type] = count
        
        # Count by level
        level_stats = {}
        for level in [1, 2, 3]:
            count = self.get_queryset().filter(level=level, is_active=True).count()
            level_stats[f'level_{level}'] = count
        
        return Response({
            'total_categories': total_categories,
            'active_categories': active_categories,
            'division_stats': division_stats,
            'level_stats': level_stats,
        })

    @action(detail=False, methods=['get'])
    def hierarchy(self, request):
        """Get categories in hierarchical structure"""
        # Get top-level categories
        top_level = self.get_queryset().filter(parent_category__isnull=True, is_active=True)
        serializer = self.get_serializer(top_level, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def subcategories(self, request, pk=None):
        """Get subcategories of a specific category"""
        category = self.get_object()
        subcategories = category.subcategories.filter(is_active=True).order_by('sort_order', 'name')
        serializer = self.get_serializer(subcategories, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def products(self, request, pk=None):
        """Get products in this category"""
        category = self.get_object()
        products = category.products.filter(status='active').order_by('name')
        # Import ProductSerializer here to avoid circular imports
        from .serializers import ProductSerializer
        serializer = ProductSerializer(products, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_division(self, request):
        """Get categories grouped by division type"""
        division_type = request.query_params.get('division_type')
        if not division_type:
            return Response({'error': 'division_type parameter is required'}, status=400)
        
        categories = self.get_queryset().filter(
            division_type=division_type, 
            is_active=True
        ).order_by('sort_order', 'name')
        
        serializer = self.get_serializer(categories, many=True)
        return Response(serializer.data)


class ProductViewSet(viewsets.ModelViewSet):
    """ViewSet for managing products and services"""
    queryset = Product.objects.all()
    serializer_class = ProductSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'product_type', 'category', 'taxable', 'track_inventory']
    search_fields = ['name', 'sku', 'description']
    ordering_fields = ['name', 'unit_price', 'created_at']
    ordering = ['name']

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Annotate total quantity from all warehouses
        queryset = queryset.annotate(
            total_quantity_on_hand=Coalesce(
                Sum('inventory_levels__quantity_on_hand'),
                Value(0, output_field=DecimalField())
            )
        )
        
        # Filter by price range
        min_price = self.request.query_params.get('min_price')
        max_price = self.request.query_params.get('max_price')
        
        if min_price is not None:
            queryset = queryset.filter(unit_price__gte=min_price)
        if max_price is not None:
            queryset = queryset.filter(unit_price__lte=max_price)
        
        # Filter by low stock
        low_stock = self.request.query_params.get('low_stock')
        if low_stock == 'true':
            queryset = queryset.filter(
                track_inventory=True,
                total_quantity_on_hand__lte=F('reorder_point')
            )
        
        return queryset

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get product statistics"""
        total_products = self.get_queryset().filter(product_type='product').count()
        total_services = self.get_queryset().filter(product_type='service').count()
        
        # Calculate inventory value using annotated total_quantity_on_hand
        inventory_value = self.get_queryset().filter(
            product_type='product',
            track_inventory=True
        ).aggregate(
            total=Sum(F('total_quantity_on_hand') * F('cost_price'))
        )['total'] or 0
        
        # Low stock items using annotated total_quantity_on_hand
        low_stock_count = self.get_queryset().filter(
            track_inventory=True,
            total_quantity_on_hand__lte=F('reorder_point')
        ).count()
        
        return Response({
            'total_products': total_products,
            'total_services': total_services,
            'inventory_value': float(inventory_value),
            'low_stock_count': low_stock_count,
        })

    @action(detail=False, methods=['get'])
    def low_stock(self, request):
        """Get products with low stock"""
        low_stock_products = self.get_queryset().filter(
            track_inventory=True,
            total_quantity_on_hand__lte=F('reorder_point')
        )
        serializer = self.get_serializer(low_stock_products, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def gl_accounts(self, request):
        """Get available GL accounts for product setup"""
        account_type = request.query_params.get('type', 'all')
        
        if account_type == 'revenue':
            accounts = Account.objects.filter(
                account_type__type='REVENUE',
                is_active=True,
                is_header_account=False
            ).order_by('account_number')
        elif account_type == 'expense':
            accounts = Account.objects.filter(
                account_type__type='EXPENSE',
                is_active=True,
                is_header_account=False
            ).order_by('account_number')
        elif account_type == 'asset':
            accounts = Account.objects.filter(
                account_type__type='ASSET',
                is_active=True,
                is_header_account=False
            ).order_by('account_number')
        else:
            # Return all accounts suitable for products
            accounts = Account.objects.filter(
                account_type__type__in=['REVENUE', 'EXPENSE', 'ASSET'],
                is_active=True,
                is_header_account=False
            ).order_by('account_type__type', 'account_number')
        
        # Simple serialization for dropdown
        account_data = [
            {
                'id': acc.id,
                'account_number': acc.account_number,
                'account_name': acc.account_name,
                'account_type': acc.account_type.type,
                'display_name': f"{acc.account_number} - {acc.account_name}"
            }
            for acc in accounts
        ]
        
        return Response(account_data)

    @action(detail=False, methods=['get'])
    def dropdown(self, request):
        """Get all active products for dropdown (optimized for search)"""
        queryset = self.get_queryset().filter(status='active').order_by('name')
        
        # Apply search if provided
        search = request.query_params.get('search', '')
        if search:
            queryset = queryset.filter(
                models.Q(name__icontains=search) |
                models.Q(sku__icontains=search) |
                models.Q(description__icontains=search)
            )
        
        # Limit results for performance
        limit = int(request.query_params.get('limit', 50))
        queryset = queryset[:limit]
        
        # Simple serialization for dropdown
        product_data = [
            {
                'id': product.id,
                'name': product.name,
                'sku': product.sku,
                'unit_price': str(product.unit_price),
                'cost_price': str(product.cost_price),
                'display_name': f"{product.name} ({product.sku})" if product.sku else product.name,
                'track_inventory': product.track_inventory,
                'quantity_on_hand': product.quantity_on_hand,
            }
            for product in queryset
        ]
        
        return Response(product_data)











class PaymentTermViewSet(viewsets.ModelViewSet):
    """ViewSet for managing payment terms"""
    queryset = PaymentTerm.objects.filter(is_active=True)
    serializer_class = PaymentTermSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'days', 'created_at']
    ordering = ['days', 'name']

    @action(detail=False, methods=['get'])
    def default(self, request):
        """Get the default payment term"""
        try:
            default_term = PaymentTerm.objects.get(is_default=True, is_active=True)
            serializer = self.get_serializer(default_term)
            return Response(serializer.data)
        except PaymentTerm.DoesNotExist:
            return Response({'error': 'No default payment term found'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def set_default(self, request, pk=None):
        """Set this payment term as default"""
        payment_term = self.get_object()
        
        # Remove default from all other terms
        PaymentTerm.objects.filter(is_default=True).update(is_default=False)
        
        # Set this term as default
        payment_term.is_default = True
        payment_term.save()
        
        serializer = self.get_serializer(payment_term)
        return Response(serializer.data)








# Enhanced Product ViewSet with Sales Price Authority
class ProductPricingViewSet(viewsets.ModelViewSet):
    """Special ViewSet for Sales Department to manage product pricing"""
    queryset = Product.objects.filter(status='active')
    serializer_class = ProductPricingSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category', 'product_type']
    search_fields = ['name', 'sku']
    ordering_fields = ['name', 'unit_price', 'margin_percentage', 'price_last_updated_at']
    ordering = ['name']
    
    http_method_names = ['get', 'patch', 'head', 'options']  # Only allow price updates, not creation/deletion

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by margin range
        min_margin = self.request.query_params.get('min_margin')
        max_margin = self.request.query_params.get('max_margin')
        
        if min_margin is not None:
            # Calculate margin percentage dynamically
            queryset = queryset.annotate(
                calculated_margin=models.Case(
                    models.When(cost_price__gt=0, then=(F('unit_price') - F('cost_price')) / F('cost_price') * 100),
                    default=0,
                    output_field=models.DecimalField()
                )
            ).filter(calculated_margin__gte=min_margin)
        
        if max_margin is not None:
            queryset = queryset.annotate(
                calculated_margin=models.Case(
                    models.When(cost_price__gt=0, then=(F('unit_price') - F('cost_price')) / F('cost_price') * 100),
                    default=0,
                    output_field=models.DecimalField()
                )
            ).filter(calculated_margin__lte=max_margin)
        
        return queryset

    @action(detail=False, methods=['get'])
    def pricing_stats(self, request):
        """Get pricing statistics for Sales Department"""
        queryset = self.get_queryset()
        
        total_products = queryset.count()
        
        # Products with no cost price (can't calculate margin)
        no_cost_price = queryset.filter(cost_price__isnull=True).count()
        
        # Products with negative margin
        negative_margin = queryset.filter(
            cost_price__gt=0,
            unit_price__lt=F('cost_price')
        ).count()
        
        # Average margin
        avg_margin = queryset.filter(cost_price__gt=0).aggregate(
            avg_margin=models.Avg(
                (F('unit_price') - F('cost_price')) / F('cost_price') * 100
            )
        )['avg_margin'] or 0
        
        # Recent price updates (last 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        recent_updates = queryset.filter(
            price_last_updated_at__gte=thirty_days_ago
        ).count()
        
        return Response({
            'total_products': total_products,
            'no_cost_price_count': no_cost_price,
            'negative_margin_count': negative_margin,
            'average_margin_percentage': float(avg_margin),
            'recent_price_updates_30_days': recent_updates,
        })

    @action(detail=False, methods=['get'])
    def margin_analysis(self, request):
        """Get detailed margin analysis"""
        queryset = self.get_queryset().filter(cost_price__gt=0)
        
        # Group by margin ranges
        margin_ranges = {
            'negative': queryset.filter(unit_price__lt=F('cost_price')).count(),
            '0_to_10': queryset.filter(
                unit_price__gte=F('cost_price'),
                unit_price__lt=F('cost_price') * 1.1
            ).count(),
            '10_to_25': queryset.filter(
                unit_price__gte=F('cost_price') * 1.1,
                unit_price__lt=F('cost_price') * 1.25
            ).count(),
            '25_to_50': queryset.filter(
                unit_price__gte=F('cost_price') * 1.25,
                unit_price__lt=F('cost_price') * 1.5
            ).count(),
            'above_50': queryset.filter(unit_price__gte=F('cost_price') * 1.5).count(),
        }
        
        return Response(margin_ranges)


# Sales Workflow ViewSets

class SalesOrderViewSet(viewsets.ModelViewSet):
    """ViewSet for managing Sales Orders"""

    queryset = SalesOrder.objects.all()
    serializer_class = SalesOrderSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'customer', 'seller_name']
    search_fields = ['so_number', 'customer__name', 'reference_number', 'notes']
    ordering_fields = ['so_date', 'expected_date', 'total_amount', 'created_at']
    ordering = ['-so_date', '-created_at']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')

        if start_date:
            queryset = queryset.filter(so_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(so_date__lte=end_date)

        return queryset.select_related('customer').prefetch_related('line_items')

    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """Get sales order dashboard statistics"""
        queryset = self.get_queryset()

        stats = {
            'total_orders': queryset.count(),
            'draft_orders': queryset.filter(status='draft').count(),
            'pending_orders': queryset.filter(status='pending').count(),
            'delivered_orders': queryset.filter(status='delivered').count(),
            'total_value': queryset.aggregate(
                total=Coalesce(Sum('total_amount'), Value(0, output_field=DecimalField()))
            )['total'],
            'pending_value': queryset.filter(status__in=['pending', 'partial']).aggregate(
                total=Coalesce(Sum('balance_due'), Value(0, output_field=DecimalField()))
            )['total'],
        }

        return Response(stats)

    @action(detail=True, methods=['post'])
    def create_delivery_note(self, request, pk=None):
        """Create a delivery note from sales order"""
        sales_order = self.get_object()

        if sales_order.status not in ['pending', 'acknowledged', 'partial']:
            return Response(
                {'error': 'Cannot create delivery note for this sales order status'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create delivery note
        delivery_note_data = {
            'sales_order': sales_order.id,
            'customer': sales_order.customer.id if sales_order.customer else None,
            'delivery_date': request.data.get('delivery_date', timezone.now().date()),
            'delivery_address': request.data.get('delivery_address', sales_order.ship_to_address),
            'notes': request.data.get('notes', ''),
        }

        delivery_note_serializer = GoodsDeliveryNoteSerializer(
            data=delivery_note_data,
            context={'request': request}
        )

        if delivery_note_serializer.is_valid():
            delivery_note = delivery_note_serializer.save()

            # Create delivery note line items from sales order line items
            for so_line_item in sales_order.line_items.all():
                remaining_qty = so_line_item.quantity - so_line_item.quantity_delivered
                if remaining_qty > 0:
                    GoodsDeliveryNoteLineItem.objects.create(
                        delivery_note=delivery_note,
                        sales_order_line_item=so_line_item,
                        product=so_line_item.product,
                        description=so_line_item.description,
                        quantity_ordered=so_line_item.quantity,
                        quantity_delivered=request.data.get('quantity_delivered', remaining_qty),
                        unit_of_measure=so_line_item.unit_of_measure,
                        unit_price=so_line_item.unit_price,
                        line_order=so_line_item.line_order
                    )

            return Response(
                GoodsDeliveryNoteSerializer(delivery_note, context={'request': request}).data,
                status=status.HTTP_201_CREATED
            )

        return Response(delivery_note_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def available_products(self, request):
        """Get available products with pricing information for sales"""
        try:
            from sales.models import Product  # Products are in sales app, not Pricing app
            from decimal import Decimal

            # Get active products from sales module
            products = Product.objects.filter(status='active')

            product_data = []

            for product in products:
                # Use cost_price as default price (avoid circular import with Pricing)
                price = product.cost_price or Decimal('0.00')

                # Get inventory availability (handle case where inventory app might not exist)
                try:
                    from inventory.models import Inventory
                    inventory_qty = Inventory.objects.filter(
                        product=product
                    ).aggregate(
                        total=Sum('quantity_on_hand')
                    )['total'] or Decimal('0.00')
                except:
                    inventory_qty = Decimal('100.00')  # Default quantity for testing

                product_data.append({
                    'id': product.id,
                    'code': product.sku or f'PROD-{product.id}',
                    'name': product.name,
                    'description': product.description or '',
                    'type': product.product_type,
                    'uom': 'each',  # Default UOM since sales model doesn't have this field
                    'unit_price': price,
                    'cost_price': product.cost_price or Decimal('0.00'),
                    'available_quantity': inventory_qty,
                    'is_available': inventory_qty > 0,
                })

            return Response(product_data)

        except Exception as e:
            return Response(
                {'error': f'Error fetching products: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GoodsDeliveryNoteViewSet(viewsets.ModelViewSet):
    """ViewSet for managing Goods Delivery Notes"""

    queryset = GoodsDeliveryNote.objects.all()
    serializer_class = GoodsDeliveryNoteSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'customer', 'sales_order']
    search_fields = ['gdn_number', 'customer__name', 'sales_order__so_number', 'notes']
    ordering_fields = ['delivery_date', 'created_at']
    ordering = ['-delivery_date', '-created_at']

    def create(self, request, *args, **kwargs):
        """Override create to add debugging"""
        print(f"\n=== DELIVERY NOTE CREATE DEBUG ===")
        print(f"User: {request.user}")
        print(f"User authenticated: {request.user.is_authenticated}")

        # Safely access request data
        try:
            request_data = getattr(request, 'data', None)
            if request_data is not None:
                print(f"Request data: {request_data}")
            else:
                print(f"Request body: {request.body.decode('utf-8') if hasattr(request, 'body') else 'No body'}")
        except Exception as e:
            print(f"Could not access request data: {e}")

        print(f"Request method: {request.method}")
        print(f"Request path: {request.path}")

        try:
            result = super().create(request, *args, **kwargs)
            print(f"✅ Create successful: {result.status_code}")
            return result
        except Exception as e:
            print(f"❌ Create failed: {e}")
            print(f"Exception type: {type(e)}")
            import traceback
            traceback.print_exc()
            raise

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')

        if start_date:
            queryset = queryset.filter(delivery_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(delivery_date__lte=end_date)

        return queryset.select_related('customer', 'sales_order').prefetch_related('line_items')

    @action(detail=True, methods=['post'])
    def confirm_delivery(self, request, pk=None):
        """Confirm delivery and update inventory"""
        from inventory.models import Warehouse
        from inventory.services import InventoryService
        from django.db import transaction as db_transaction

        delivery_note = self.get_object()

        if delivery_note.status != 'draft':
            return Response(
                {'error': 'Can only confirm draft delivery notes'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            with db_transaction.atomic():
                # Update status to delivered
                delivery_note.status = 'delivered'
                delivery_note.actual_delivery_date = timezone.now().date()
                delivery_note.received_by = request.data.get('received_by', '')
                delivery_note.received_date = timezone.now()
                delivery_note.customer_signature = request.data.get('customer_signature', '')
                delivery_note.save()

                # Create stock transactions for each line item
                warehouse = Warehouse.objects.filter(is_active=True).first()
                if not warehouse:
                    return Response(
                        {'error': 'No active warehouse found'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                for line_item in delivery_note.line_items.all():
                    if line_item.product and line_item.quantity_delivered > 0:
                        try:
                            # Create stock issue transaction
                            InventoryService.issue_stock(
                                product=line_item.product,
                                warehouse=warehouse,
                                quantity=line_item.quantity_delivered,
                                reference_type='DELIVERY',
                                reference_id=delivery_note.id,
                                user=request.user,
                                description=f"Stock issue for delivery {delivery_note.gdn_number}"
                            )

                            # Update sales order line item delivered quantity
                            if line_item.sales_order_line_item:
                                so_line = line_item.sales_order_line_item
                                so_line.quantity_delivered += line_item.quantity_delivered
                                so_line.save()

                        except Exception as e:
                            print(f"Error creating stock transaction for {line_item.product.name}: {e}")
                            # Continue with other items, don't fail the entire delivery

                # Update sales order delivery status
                sales_order = delivery_note.sales_order
                if sales_order:
                    # Check if all items are delivered
                    all_delivered = True
                    for line_item in sales_order.line_items.all():
                        if line_item.quantity_delivered < line_item.quantity:
                            all_delivered = False
                            break

                    if all_delivered:
                        sales_order.status = 'delivered'
                    else:
                        sales_order.status = 'partial'

                    sales_order.save()

                return Response(
                    GoodsDeliveryNoteSerializer(delivery_note, context={'request': request}).data
                )

        except Exception as e:
            return Response(
                {'error': f'Failed to confirm delivery: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def create_invoice(self, request, pk=None):
        """Create customer invoice from delivery note"""
        delivery_note = self.get_object()

        if delivery_note.status != 'delivered':
            return Response(
                {'error': 'Can only create invoice from delivered goods'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create invoice
        invoice_data = {
            'customer': delivery_note.customer.id if delivery_note.customer else None,
            'sales_order': delivery_note.sales_order.id if delivery_note.sales_order else None,
            'delivery_note': delivery_note.id,
            'invoice_date': request.data.get('invoice_date', timezone.now().date()),
            'due_date': request.data.get('due_date'),
            'payment_terms': request.data.get('payment_terms', ''),
            'notes': request.data.get('notes', ''),
        }

        invoice_serializer = CustomerInvoiceSerializer(
            data=invoice_data,
            context={'request': request}
        )

        if invoice_serializer.is_valid():
            invoice = invoice_serializer.save()

            # Create invoice line items from delivery note line items
            for gdn_line_item in delivery_note.line_items.all():
                CustomerInvoiceLineItem.objects.create(
                    invoice=invoice,
                    delivery_note_line_item=gdn_line_item,
                    product=gdn_line_item.product,
                    description=gdn_line_item.description,
                    quantity=gdn_line_item.quantity_delivered,
                    unit_of_measure=gdn_line_item.unit_of_measure,
                    unit_price=gdn_line_item.unit_price,
                    line_order=gdn_line_item.line_order
                )

            # Calculate invoice totals
            invoice.calculate_totals()

            return Response(
                CustomerInvoiceSerializer(invoice, context={'request': request}).data,
                status=status.HTTP_201_CREATED
            )

        return Response(invoice_serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CustomerInvoiceViewSet(viewsets.ModelViewSet):
    """ViewSet for managing Customer Invoices"""

    queryset = CustomerInvoice.objects.all()
    serializer_class = CustomerInvoiceSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'invoice_type', 'customer', 'sales_order']
    search_fields = ['invoice_number', 'customer__name', 'reference_number', 'notes']
    ordering_fields = ['invoice_date', 'due_date', 'total_amount', 'created_at']
    ordering = ['-invoice_date', '-created_at']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')

        if start_date:
            queryset = queryset.filter(invoice_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(invoice_date__lte=end_date)

        # Filter by invoice type
        invoice_type = self.request.query_params.get('invoice_type')
        if invoice_type:
            queryset = queryset.filter(invoice_type=invoice_type)

        return queryset.select_related('customer', 'sales_order', 'delivery_note').prefetch_related('line_items')

    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """Get customer invoice dashboard statistics"""
        queryset = self.get_queryset()

        stats = {
            'total_invoices': queryset.count(),
            'draft_invoices': queryset.filter(status='draft').count(),
            'sent_invoices': queryset.filter(status='sent').count(),
            'paid_invoices': queryset.filter(status='paid').count(),
            'overdue_invoices': queryset.filter(
                status__in=['sent', 'viewed', 'partial'],
                due_date__lt=timezone.now().date()
            ).count(),
            'total_revenue': queryset.filter(invoice_type='invoice').aggregate(
                total=Coalesce(Sum('total_amount'), Value(0, output_field=DecimalField()))
            )['total'],
            'outstanding_amount': queryset.filter(
                invoice_type='invoice',
                status__in=['sent', 'viewed', 'partial']
            ).aggregate(
                total=Coalesce(Sum('balance_due'), Value(0, output_field=DecimalField()))
            )['total'],
        }

        return Response(stats)

    @action(detail=True, methods=['post'])
    def mark_as_sent(self, request, pk=None):
        """Mark invoice as sent"""
        invoice = self.get_object()

        if invoice.status != 'draft':
            return Response(
                {'error': 'Can only send draft invoices'},
                status=status.HTTP_400_BAD_REQUEST
            )

        invoice.status = 'sent'
        invoice.email_sent = True
        invoice.email_sent_date = timezone.now()
        invoice.save()

        return Response(
            CustomerInvoiceSerializer(invoice, context={'request': request}).data
        )

    @action(detail=True, methods=['post'])
    def record_payment(self, request, pk=None):
        """Record payment for invoice"""
        invoice = self.get_object()

        payment_amount = request.data.get('payment_amount', 0)
        payment_date = request.data.get('payment_date', timezone.now().date())
        payment_method = request.data.get('payment_method', '')

        if payment_amount <= 0:
            return Response(
                {'error': 'Payment amount must be greater than 0'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if payment_amount > invoice.balance_due:
            return Response(
                {'error': 'Payment amount cannot exceed balance due'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update invoice payment information
        invoice.amount_paid += payment_amount
        invoice.balance_due = invoice.total_amount - invoice.amount_paid
        invoice.payment_date = payment_date
        invoice.payment_method = payment_method

        # Update status based on payment
        if invoice.balance_due <= 0:
            invoice.status = 'paid'
        else:
            invoice.status = 'partial'

        invoice.save()

        return Response(
            CustomerInvoiceSerializer(invoice, context={'request': request}).data
        )


class GoodsDeliveryReturnNoteViewSet(viewsets.ModelViewSet):
    """ViewSet for managing Goods Delivery Return Notes"""

    queryset = GoodsDeliveryReturnNote.objects.all()
    serializer_class = GoodsDeliveryReturnNoteSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'return_reason', 'customer', 'delivery_note']
    search_fields = ['gdrn_number', 'customer__name', 'delivery_note__gdn_number', 'notes']
    ordering_fields = ['return_date', 'created_at']
    ordering = ['-return_date', '-created_at']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')

        if start_date:
            queryset = queryset.filter(return_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(return_date__lte=end_date)

        return queryset.select_related('customer', 'delivery_note').prefetch_related('line_items')

    @action(detail=True, methods=['post'])
    def confirm_return(self, request, pk=None):
        """Confirm return and update inventory"""
        return_note = self.get_object()

        if return_note.status != 'draft':
            return Response(
                {'error': 'Can only confirm draft return notes'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update status to received
        return_note.status = 'received'
        return_note.actual_return_date = timezone.now().date()
        return_note.quality_check_passed = request.data.get('quality_check_passed', False)
        return_note.quality_check_notes = request.data.get('quality_check_notes', '')
        return_note.quality_checked_by = request.data.get('quality_checked_by', '')
        return_note.quality_check_date = timezone.now()
        return_note.save()

        return Response(
            GoodsDeliveryReturnNoteSerializer(return_note, context={'request': request}).data
        )

    @action(detail=True, methods=['post'])
    def create_credit_note(self, request, pk=None):
        """Create customer credit note from return note"""
        return_note = self.get_object()

        if return_note.status != 'received':
            return Response(
                {'error': 'Can only create credit note from received returns'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create credit note
        credit_note_data = {
            'customer': return_note.customer.id if return_note.customer else None,
            'delivery_note': return_note.delivery_note.id if return_note.delivery_note else None,
            'invoice_type': 'credit',
            'invoice_date': request.data.get('credit_date', timezone.now().date()),
            'due_date': request.data.get('due_date', timezone.now().date()),
            'notes': request.data.get('notes', f'Credit for return - {return_note.gdrn_number}'),
        }

        credit_note_serializer = CustomerInvoiceSerializer(
            data=credit_note_data,
            context={'request': request}
        )

        if credit_note_serializer.is_valid():
            credit_note = credit_note_serializer.save()

            # Create credit note line items from return note line items
            for return_line_item in return_note.line_items.all():
                CustomerInvoiceLineItem.objects.create(
                    invoice=credit_note,
                    product=return_line_item.product,
                    description=f'Credit: {return_line_item.description}',
                    quantity=return_line_item.quantity_returned,
                    unit_of_measure=return_line_item.unit_of_measure,
                    unit_price=return_line_item.unit_price,
                    line_order=return_line_item.line_order
                )

            # Calculate credit note totals
            credit_note.calculate_totals()

            return Response(
                CustomerInvoiceSerializer(credit_note, context={'request': request}).data,
                status=status.HTTP_201_CREATED
            )

        return Response(credit_note_serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CustomerBillViewSet(viewsets.ModelViewSet):
    """ViewSet for managing Customer Bills"""

    queryset = CustomerBill.objects.all()
    serializer_class = CustomerBillSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'bill_type', 'customer', 'sales_order']
    search_fields = ['bill_number', 'customer__name', 'reference_number', 'notes']
    ordering_fields = ['bill_date', 'due_date', 'total_amount', 'created_at']
    ordering = ['-bill_date', '-created_at']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')

        if start_date:
            queryset = queryset.filter(bill_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(bill_date__lte=end_date)

        # Filter by bill type
        bill_type = self.request.query_params.get('bill_type')
        if bill_type:
            queryset = queryset.filter(bill_type=bill_type)

        return queryset.select_related('customer', 'sales_order').prefetch_related('line_items')

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get customer bill statistics"""
        queryset = self.get_queryset()

        stats = {
            'total_bills': queryset.count(),
            'draft_bills': queryset.filter(status='draft').count(),
            'posted_bills': queryset.filter(status='posted').count(),
            'paid_bills': queryset.filter(status='paid').count(),
            'total_receivables': queryset.filter(bill_type='bill').aggregate(
                total=Coalesce(Sum('total_amount'), Value(0, output_field=DecimalField()))
            )['total'],
            'outstanding_amount': queryset.filter(
                bill_type='bill',
                status__in=['posted']
            ).aggregate(
                total=Coalesce(Sum('balance_due'), Value(0, output_field=DecimalField()))
            )['total'],
        }

        return Response(stats)

    @action(detail=True, methods=['post'])
    def post(self, request, pk=None):
        """Post customer bill to create GL entries"""
        customer_bill = self.get_object()

        if customer_bill.status != 'draft':
            return Response(
                {'error': 'Can only post draft bills'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update status to posted
        customer_bill.status = 'posted'
        customer_bill.save()  # This will trigger GL entry creation

        return Response(
            CustomerBillSerializer(customer_bill, context={'request': request}).data
        )

    @action(detail=True, methods=['post'])
    def mark_paid(self, request, pk=None):
        """Mark customer bill as paid"""
        customer_bill = self.get_object()
        payment_data = request.data

        payment_amount = float(payment_data.get('payment_amount', customer_bill.balance_due))

        # Update payment information
        customer_bill.amount_paid += payment_amount
        customer_bill.save()  # This will recalculate balance_due and update status

        return Response({'status': 'Customer bill marked as paid'})

    @action(detail=True, methods=['post'])
    def duplicate(self, request, pk=None):
        """Duplicate customer bill"""
        original_bill = self.get_object()

        # Create a copy of the bill
        new_bill = CustomerBill.objects.create(
            customer=original_bill.customer,
            bill_type=original_bill.bill_type,
            bill_date=original_bill.bill_date,
            due_date=original_bill.due_date,
            sales_order=original_bill.sales_order,
            delivery_note=original_bill.delivery_note,
            delivery_return_note=original_bill.delivery_return_note,
            status='draft',  # Always create as draft
            payment_terms=original_bill.payment_terms,
            reference_number=f"Copy of {original_bill.reference_number}" if original_bill.reference_number else None,
            notes=original_bill.notes,
            source_type=original_bill.source_type,
            created_by=request.user
        )

        # Copy line items
        for item in original_bill.line_items.all():
            CustomerBillItem.objects.create(
                customer_bill=new_bill,
                product=item.product,
                item_description=item.item_description,
                quantity=item.quantity,
                unit_price=item.unit_price,
                taxable=item.taxable,
                tax_rate=item.tax_rate,
                account_code=item.account_code,
                line_order=item.line_order
            )

        # Recalculate totals
        new_bill.calculate_totals()
        new_bill.save()

        return Response(
            CustomerBillSerializer(new_bill, context={'request': request}).data,
            status=status.HTTP_201_CREATED
        )

    @action(detail=True, methods=['post'])
    def send(self, request, pk=None):
        """Send customer bill via email"""
        customer_bill = self.get_object()
        email_data = request.data

        # TODO: Implement email sending logic
        # For now, just return success
        return Response({'status': 'Customer bill sent successfully'})

    @action(detail=False, methods=['post'])
    def create_from_sales_order(self, request):
        """Create customer bill from sales order"""
        sales_order_id = request.data.get('sales_order_id')

        if not sales_order_id:
            return Response(
                {'error': 'sales_order_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            sales_order = SalesOrder.objects.get(id=sales_order_id)
        except SalesOrder.DoesNotExist:
            return Response(
                {'error': 'Sales order not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Create customer bill from sales order
        customer_bill = CustomerBill.objects.create(
            customer=sales_order.customer,
            bill_type='bill',
            bill_date=request.data.get('bill_date', sales_order.order_date),
            due_date=request.data.get('due_date', sales_order.delivery_date),
            sales_order=sales_order,
            status='draft',
            payment_terms=sales_order.payment_terms,
            reference_number=f"SO-{sales_order.so_number}",
            notes=request.data.get('notes', f"Bill created from Sales Order {sales_order.so_number}"),
            source_type='sales_order',
            created_by=request.user
        )

        # Copy line items from sales order
        for so_item in sales_order.line_items.all():
            CustomerBillItem.objects.create(
                customer_bill=customer_bill,
                product=so_item.product,
                item_description=so_item.item_description,
                quantity=so_item.quantity,
                unit_price=so_item.unit_price,
                taxable=True,  # Default to taxable
                tax_rate=so_item.tax_rate,
                account_code=getattr(so_item.product, 'revenue_account_code', '4000-SALES') if so_item.product else '4000-SALES',
                line_order=so_item.line_order
            )

        # Recalculate totals
        customer_bill.calculate_totals()
        customer_bill.save()

        return Response(
            CustomerBillSerializer(customer_bill, context={'request': request}).data,
            status=status.HTTP_201_CREATED
        )

    @action(detail=False, methods=['post'])
    def create_from_delivery_note(self, request):
        """Create customer bill from delivery note"""
        delivery_note_id = request.data.get('delivery_note_id')

        if not delivery_note_id:
            return Response(
                {'error': 'delivery_note_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            delivery_note = GoodsDeliveryNote.objects.get(id=delivery_note_id)
        except GoodsDeliveryNote.DoesNotExist:
            return Response(
                {'error': 'Delivery note not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Create customer bill from delivery note
        customer_bill = CustomerBill.objects.create(
            customer=delivery_note.customer,
            bill_type='bill',
            bill_date=request.data.get('bill_date', delivery_note.delivery_date),
            due_date=request.data.get('due_date', delivery_note.delivery_date),
            sales_order=delivery_note.sales_order,
            delivery_note=delivery_note,
            status='draft',
            payment_terms=delivery_note.sales_order.payment_terms if delivery_note.sales_order else None,
            reference_number=f"DN-{delivery_note.gdn_number}",
            notes=request.data.get('notes', f"Bill created from Delivery Note {delivery_note.gdn_number}"),
            source_type='delivery_note',
            created_by=request.user
        )

        # Copy line items from delivery note
        for dn_item in delivery_note.line_items.all():
            CustomerBillItem.objects.create(
                customer_bill=customer_bill,
                product=dn_item.product,
                item_description=dn_item.item_description,
                quantity=dn_item.quantity_delivered,
                unit_price=dn_item.unit_price,
                taxable=True,  # Default to taxable
                tax_rate=getattr(dn_item.product, 'tax_rate', 0) if dn_item.product else 0,
                account_code=getattr(dn_item.product, 'revenue_account_code', '4000-SALES') if dn_item.product else '4000-SALES',
                line_order=dn_item.line_order
            )

        # Recalculate totals
        customer_bill.calculate_totals()
        customer_bill.save()

        return Response(
            CustomerBillSerializer(customer_bill, context={'request': request}).data,
            status=status.HTTP_201_CREATED
        )

    @action(detail=False, methods=['get'])
    def billable_sales_orders(self, request):
        """Get sales orders that can be billed"""
        # Get sales orders that don't have bills yet or are partially billed
        billable_orders = SalesOrder.objects.filter(
            status__in=['confirmed', 'delivered']
        ).exclude(
            customer_bills__isnull=False
        ).select_related('customer')

        # Serialize the data
        data = []
        for order in billable_orders:
            data.append({
                'id': order.id,
                'so_number': order.so_number,
                'customer_name': order.customer.name if order.customer else 'Unknown',
                'order_date': order.order_date,
                'total_amount': order.total_amount,
                'status': order.status
            })

        return Response(data)

    @action(detail=False, methods=['get'])
    def billable_delivery_notes(self, request):
        """Get delivery notes that can be billed"""
        # Get delivery notes that don't have bills yet
        billable_notes = GoodsDeliveryNote.objects.filter(
            status='delivered'
        ).exclude(
            customer_bills__isnull=False
        ).select_related('customer', 'sales_order')

        # Serialize the data
        data = []
        for note in billable_notes:
            data.append({
                'id': note.id,
                'gdn_number': note.gdn_number,
                'customer_name': note.customer.name if note.customer else 'Unknown',
                'delivery_date': note.delivery_date,
                'sales_order_number': note.sales_order.so_number if note.sales_order else None,
                'status': note.status
            })

        return Response(data)

    @action(detail=False, methods=['get'])
    def overdue(self, request):
        """Get overdue customer bills"""
        from datetime import date

        overdue_bills = self.get_queryset().filter(
            status='posted',
            due_date__lt=date.today(),
            balance_due__gt=0
        )

        serializer = self.get_serializer(overdue_bills, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get customer bills summary for dashboard"""
        from datetime import date, timedelta

        queryset = self.get_queryset()

        summary = {
            'total_bills': queryset.count(),
            'total_amount': queryset.aggregate(
                total=Coalesce(Sum('total_amount'), Value(0, output_field=DecimalField()))
            )['total'],
            'paid_amount': queryset.aggregate(
                total=Coalesce(Sum('amount_paid'), Value(0, output_field=DecimalField()))
            )['total'],
            'outstanding_amount': queryset.filter(
                status='posted',
                balance_due__gt=0
            ).aggregate(
                total=Coalesce(Sum('balance_due'), Value(0, output_field=DecimalField()))
            )['total'],
            'overdue_amount': queryset.filter(
                status='posted',
                due_date__lt=date.today(),
                balance_due__gt=0
            ).aggregate(
                total=Coalesce(Sum('balance_due'), Value(0, output_field=DecimalField()))
            )['total'],
        }

        return Response(summary)
