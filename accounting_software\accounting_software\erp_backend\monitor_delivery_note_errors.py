#!/usr/bin/env python3
"""
Monitor Django server logs for delivery note creation errors
"""
import os
import sys
import django
import time
import threading
from datetime import datetime

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

# Import after Django setup
from django.db import connection
from sales.models import GoodsDeliveryNote
from sales.views import GoodsDeliveryNoteViewSet

def monitor_database_queries():
    """Monitor database queries for delivery note operations"""
    print("🔍 Monitoring database queries...")
    
    # Enable query logging
    from django.conf import settings
    settings.LOGGING['loggers']['django.db.backends'] = {
        'level': 'DEBUG',
        'handlers': ['console'],
    }

def test_model_validation():
    """Test model validation directly"""
    print("\n🧪 Testing model validation...")
    
    try:
        from sales.models import SalesOrder
        from contacts.models import Contact
        
        # Get test data
        so = SalesOrder.objects.filter(id=20).first()
        customer = Contact.objects.filter(id=87).first()
        
        if not so:
            print("❌ Sales Order 20 not found")
            return
            
        if not customer:
            print("❌ Customer 87 not found")
            return
            
        print(f"✅ Sales Order found: {so.so_number}")
        print(f"✅ Customer found: {customer.name}")
        
        # Test model creation with exact data from frontend
        from datetime import date, timedelta
        
        test_data = {
            'sales_order': so,
            'customer': customer,
            'delivery_date': date.today(),
            'expected_delivery_date': date.today() + timedelta(days=1),
            'delivery_address': '',
            'delivery_contact_person': '',
            'delivery_contact_phone': '',
            'vehicle_number': '',
            'driver_name': '',
            'driver_phone': '',
            'notes': '',
            'internal_notes': '',
            'status': 'draft'
        }
        
        print("🧪 Testing model creation...")
        gdn = GoodsDeliveryNote(**test_data)
        gdn.full_clean()  # This will raise validation errors if any
        gdn.save()
        
        print(f"✅ Model creation successful: {gdn.gdn_number}")
        
        # Clean up
        gdn.delete()
        
    except Exception as e:
        print(f"❌ Model validation failed: {e}")
        import traceback
        traceback.print_exc()

def test_serializer_validation():
    """Test serializer validation"""
    print("\n🧪 Testing serializer validation...")
    
    try:
        from sales.serializers import GoodsDeliveryNoteSerializer
        from datetime import date, timedelta
        
        # Test data that matches frontend
        test_data = {
            'sales_order': 20,
            'customer': 87,
            'delivery_date': str(date.today()),
            'expected_delivery_date': str(date.today() + timedelta(days=1)),
            'delivery_address': '',
            'delivery_contact_person': '',
            'delivery_contact_phone': '',
            'vehicle_number': '',
            'driver_name': '',
            'driver_phone': '',
            'notes': '',
            'internal_notes': '',
            'status': 'draft'
        }
        
        print("🧪 Testing serializer...")
        serializer = GoodsDeliveryNoteSerializer(data=test_data)
        
        if serializer.is_valid():
            print("✅ Serializer validation passed")
            
            # Test save
            from django.contrib.auth.models import User
            user = User.objects.first()
            
            # Mock request context
            class MockRequest:
                def __init__(self, user):
                    self.user = user
            
            serializer.context['request'] = MockRequest(user)
            instance = serializer.save()
            
            print(f"✅ Serializer save successful: {instance.gdn_number}")
            
            # Clean up
            instance.delete()
            
        else:
            print(f"❌ Serializer validation failed: {serializer.errors}")
            
    except Exception as e:
        print(f"❌ Serializer test failed: {e}")
        import traceback
        traceback.print_exc()

def test_view_directly():
    """Test the view directly"""
    print("\n🧪 Testing view directly...")
    
    try:
        from django.test import RequestFactory
        from django.contrib.auth.models import User
        from datetime import date, timedelta
        import json
        
        factory = RequestFactory()
        user = User.objects.first()
        
        test_data = {
            'sales_order': 20,
            'customer': 87,
            'delivery_date': str(date.today()),
            'expected_delivery_date': str(date.today() + timedelta(days=1)),
            'delivery_address': '',
            'delivery_contact_person': '',
            'delivery_contact_phone': '',
            'vehicle_number': '',
            'driver_name': '',
            'driver_phone': '',
            'notes': '',
            'internal_notes': '',
            'status': 'draft'
        }
        
        # Create request
        request = factory.post(
            '/api/sales/delivery-notes/',
            data=json.dumps(test_data),
            content_type='application/json'
        )
        request.user = user
        
        # Test view
        view = GoodsDeliveryNoteViewSet()
        view.request = request
        
        print("🧪 Testing view create method...")
        response = view.create(request)
        
        print(f"✅ View test successful: {response.status_code}")
        
        if hasattr(response, 'data'):
            print(f"Response data: {response.data}")
            
            # Clean up if created
            if response.status_code == 201 and 'id' in response.data:
                GoodsDeliveryNote.objects.filter(id=response.data['id']).delete()
        
    except Exception as e:
        print(f"❌ View test failed: {e}")
        import traceback
        traceback.print_exc()

def check_database_constraints():
    """Check database constraints and foreign keys"""
    print("\n🔍 Checking database constraints...")
    
    try:
        from sales.models import SalesOrder
        from contacts.models import Contact
        
        # Check if referenced objects exist
        so_exists = SalesOrder.objects.filter(id=20).exists()
        customer_exists = Contact.objects.filter(id=87).exists()
        
        print(f"Sales Order 20 exists: {so_exists}")
        print(f"Customer 87 exists: {customer_exists}")
        
        if so_exists:
            so = SalesOrder.objects.get(id=20)
            print(f"Sales Order details: {so.so_number}, Status: {so.status}")
            
        if customer_exists:
            customer = Contact.objects.get(id=87)
            print(f"Customer details: {customer.name}, Type: {customer.contact_type}")
            
    except Exception as e:
        print(f"❌ Database constraint check failed: {e}")

def main():
    print("🚨 DELIVERY NOTE ERROR MONITOR 🚨")
    print("=" * 50)
    
    # Run all tests
    check_database_constraints()
    test_model_validation()
    test_serializer_validation()
    test_view_directly()
    
    print("\n" + "=" * 50)
    print("✅ All tests completed. Check results above.")
    print("\nIf all tests pass, the issue might be:")
    print("1. Authentication/permission issues")
    print("2. Middleware interference")
    print("3. Request format differences")
    print("4. CORS/network issues")

if __name__ == '__main__':
    main()
