import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';
import {
  People as PeopleIcon,
  Business as BusinessIcon,
  ShoppingCart as ProductIcon,
  AttachMoney as PricingIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import ContactsAdapterService from '../../../services/contacts-adapter.service';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`contacts-tabpanel-${index}`}
      aria-labelledby={`contacts-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const ContactsDemoPage: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [systemAvailable, setSystemAvailable] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<any>(null);
  const [customers, setCustomers] = useState<any[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  const [pricing, setPricing] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newCustomerName, setNewCustomerName] = useState('');
  const [newCustomerEmail, setNewCustomerEmail] = useState('');

  useEffect(() => {
    checkSystemAvailability();
  }, []);

  const checkSystemAvailability = async () => {
    setLoading(true);
    try {
      const available = await ContactsAdapterService.testConnection();
      setSystemAvailable(available);
      
      if (available) {
        const systemStats = await ContactsAdapterService.getSystemStats();
        setStats(systemStats);
        loadData();
      }
    } catch (err: any) {
      setError(err.message);
      setSystemAvailable(false);
    } finally {
      setLoading(false);
    }
  };

  const loadData = async () => {
    try {
      // Use relative URLs in development for Vite proxy
      const getApiUrl = (path: string) => import.meta.env.DEV ? path : `http://localhost:8000${path}`;

      const [customersData, productsData, pricingData] = await Promise.all([
        fetch(getApiUrl('/api/contacts/customers/')).then(r => r.json()),
        fetch(getApiUrl('/api/contacts/products-services/')).then(r => r.json()),
        fetch(getApiUrl('/api/contacts/price-list-entries/')).then(r => r.json())
      ]);

      setCustomers(customersData.results || customersData || []);
      setProducts(productsData.results || productsData || []);
      setPricing(pricingData.results || pricingData || []);
    } catch (err: any) {
      console.error('Error loading data:', err);
    }
  };

  const createSampleCustomer = async () => {
    if (!newCustomerName || !newCustomerEmail) return;
    
    setLoading(true);
    try {
      const customerFormData = {
        firstName: newCustomerName.split(' ')[0] || '',
        lastName: newCustomerName.split(' ').slice(1).join(' ') || '',
        displayName: newCustomerName,
        email: newCustomerEmail,
        companyName: '',
        phone: '******-0123',
        mobile: '',
        website: '',
        billingAddress: {
          street: '123 Test Street',
          city: 'Test City',
          state: 'Test State',
          postalCode: '12345',
          country: 'US',
        },
        shippingAddress: {
          sameAsBilling: true,
          street: '',
          city: '',
          state: '',
          postalCode: '',
          country: 'US',
        },
        paymentTerms: 'net_30',
        creditLimit: 5000,
        taxId: '',
        notes: 'Created via demo page',
        attachments: [],
        title: '',
        middleName: '',
        suffix: '',
        fax: '',
        other: '',
        nameOnCheques: '',
        isSubCustomer: false,
      };

      await ContactsAdapterService.createCustomer(customerFormData);
      setCreateDialogOpen(false);
      setNewCustomerName('');
      setNewCustomerEmail('');
      loadData(); // Refresh data
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  if (loading && systemAvailable === null) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Checking centralized contacts system...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        🏢 Centralized Contacts System Demo
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        This page demonstrates the new centralized contacts system working alongside your existing components.
      </Typography>

      {/* System Status */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" alignItems="center" gap={2}>
            {systemAvailable ? (
              <>
                <SuccessIcon color="success" />
                <Typography variant="h6" color="success.main">
                  ✅ Centralized System Active
                </Typography>
              </>
            ) : (
              <>
                <ErrorIcon color="error" />
                <Typography variant="h6" color="error.main">
                  ❌ Using Legacy System
                </Typography>
              </>
            )}
          </Box>
          
          {systemAvailable && stats && (
            <Grid container spacing={2} sx={{ mt: 2 }}>
              <Grid item xs={6} sm={3}>
                <Chip 
                  icon={<PeopleIcon />} 
                  label={`${stats.totalContacts} Contacts`} 
                  color="primary" 
                  variant="outlined" 
                />
              </Grid>
              <Grid item xs={6} sm={3}>
                <Chip 
                  icon={<BusinessIcon />} 
                  label={`${stats.totalCustomers} Customers`} 
                  color="secondary" 
                  variant="outlined" 
                />
              </Grid>
              <Grid item xs={6} sm={3}>
                <Chip 
                  icon={<ProductIcon />} 
                  label={`${stats.totalProducts} Products`} 
                  color="info" 
                  variant="outlined" 
                />
              </Grid>
              <Grid item xs={6} sm={3}>
                <Chip 
                  icon={<PricingIcon />} 
                  label={`${stats.totalPriceEntries} Price Entries`} 
                  color="warning" 
                  variant="outlined" 
                />
              </Grid>
            </Grid>
          )}
        </CardContent>
      </Card>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {systemAvailable && (
        <>
          {/* Action Buttons */}
          <Box sx={{ mb: 3 }}>
            <Button 
              variant="contained" 
              onClick={() => setCreateDialogOpen(true)}
              sx={{ mr: 2 }}
            >
              Create Sample Customer
            </Button>
            <Button 
              variant="outlined" 
              onClick={loadData}
              disabled={loading}
            >
              Refresh Data
            </Button>
          </Box>

          {/* Data Tabs */}
          <Card>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs value={tabValue} onChange={handleTabChange}>
                <Tab label={`Customers (${customers.length})`} />
                <Tab label={`Products (${products.length})`} />
                <Tab label={`Pricing (${pricing.length})`} />
              </Tabs>
            </Box>

            <TabPanel value={tabValue} index={0}>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Customer Code</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Email</TableCell>
                      <TableCell>Credit Limit</TableCell>
                      <TableCell>Payment Terms</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {customers.map((customer) => (
                      <TableRow key={customer.id}>
                        <TableCell>{customer.customer_code}</TableCell>
                        <TableCell>{customer.contact?.name || 'N/A'}</TableCell>
                        <TableCell>{customer.contact?.email || 'N/A'}</TableCell>
                        <TableCell>${customer.credit_limit}</TableCell>
                        <TableCell>{customer.payment_terms}</TableCell>
                      </TableRow>
                    ))}
                    {customers.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={5} align="center">
                          No customers found. Create one using the button above!
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>SKU</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>Category</TableCell>
                      <TableCell>Base Price</TableCell>
                      <TableCell>Status</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {products.map((product) => (
                      <TableRow key={product.id}>
                        <TableCell>{product.sku}</TableCell>
                        <TableCell>{product.name}</TableCell>
                        <TableCell>
                          <Chip 
                            label={product.type} 
                            color={product.type === 'product' ? 'primary' : 'secondary'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{product.category}</TableCell>
                        <TableCell>${product.base_price}</TableCell>
                        <TableCell>
                          <Chip 
                            label={product.status} 
                            color={product.status === 'active' ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                    {products.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={6} align="center">
                          No products found.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </TabPanel>

            <TabPanel value={tabValue} index={2}>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Product</TableCell>
                      <TableCell>Price Type</TableCell>
                      <TableCell>Customer/Vendor</TableCell>
                      <TableCell>Price</TableCell>
                      <TableCell>Currency</TableCell>
                      <TableCell>Status</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {pricing.map((price) => (
                      <TableRow key={price.id}>
                        <TableCell>{price.product_service?.name || 'N/A'}</TableCell>
                        <TableCell>
                          <Chip 
                            label={price.price_type} 
                            color={
                              price.price_type === 'general' ? 'default' :
                              price.price_type === 'customer' ? 'primary' : 'secondary'
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {price.customer?.customer_code || price.vendor?.vendor_code || 'General'}
                        </TableCell>
                        <TableCell>${price.price}</TableCell>
                        <TableCell>{price.currency}</TableCell>
                        <TableCell>
                          <Chip 
                            label={price.is_active ? 'Active' : 'Inactive'} 
                            color={price.is_active ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                    {pricing.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={6} align="center">
                          No pricing entries found.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </TabPanel>
          </Card>
        </>
      )}

      {/* Create Customer Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)}>
        <DialogTitle>Create Sample Customer</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Customer Name"
            fullWidth
            variant="outlined"
            value={newCustomerName}
            onChange={(e) => setNewCustomerName(e.target.value)}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Email"
            type="email"
            fullWidth
            variant="outlined"
            value={newCustomerEmail}
            onChange={(e) => setNewCustomerEmail(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={createSampleCustomer} 
            variant="contained"
            disabled={loading || !newCustomerName || !newCustomerEmail}
          >
            {loading ? <CircularProgress size={20} /> : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {!systemAvailable && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              💡 Integration Instructions
            </Typography>
            <Typography variant="body2" paragraph>
              The centralized contacts system is not available. To enable it:
            </Typography>
            <ol>
              <li>Make sure the Django backend is running on port 8000</li>
              <li>Ensure the new contacts app is properly configured</li>
              <li>Run the sample data creation script</li>
              <li>Refresh this page</li>
            </ol>
            <Typography variant="body2" color="text.secondary">
              Your existing forms will continue to work with the legacy system.
            </Typography>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default ContactsDemoPage; 