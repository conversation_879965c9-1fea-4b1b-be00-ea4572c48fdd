#!/usr/bin/env python
"""
Test script to debug delivery note API endpoint
"""
import os
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

def test_api_endpoint():
    """Test the delivery note API endpoint exactly like frontend"""

    # Test data from frontend - exactly as frontend sends it
    url = 'http://localhost:8000/api/sales/delivery-notes/'
    data = {
        'sales_order': 20,
        'customer': 87,
        'delivery_date': '2025-07-14',
        'expected_delivery_date': '2025-07-15',
        'delivery_address': '17 Fallway road,Brampton',
        'delivery_contact_person': 'Farah Naz',
        'delivery_contact_phone': '03342217839',
        'vehicle_number': '',
        'driver_name': '',
        'driver_phone': '',
        'notes': '',
        'internal_notes': '',
        'status': 'draft'
    }

    # Get token first
    token_url = 'http://localhost:8000/api-token-auth/'
    login_data = {'username': 'admin', 'password': 'admin123'}

    try:
        print('Getting authentication token...')
        token_response = requests.post(token_url, json=login_data)
        
        if token_response.status_code == 200:
            token = token_response.json().get('token')
            print('✅ Token obtained')

            headers = {
                'Authorization': f'Token {token}',
                'Content-Type': 'application/json',
                'Origin': 'http://localhost:5173',  # Simulate frontend origin
                'Referer': 'http://localhost:5173/'
            }

            print('Testing delivery note creation via API (simulating frontend)...')
            response = requests.post(url, json=data, headers=headers)
            
            print(f'Status Code: {response.status_code}')
            print(f'Response Headers: {dict(response.headers)}')
            print(f'Response Text: {response.text[:1000]}')
            
            if response.status_code == 201:
                print('✅ API call successful')
                result = response.json()
                print(f'Created delivery note: {result.get("gdn_number")}')
            else:
                print(f'❌ API call failed: {response.status_code}')
                
        else:
            print(f'❌ Failed to get token: {token_response.status_code}')
            print(f'Response: {token_response.text}')
            
    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        traceback.print_exc()

def test_frontend_scenario():
    """Test the exact frontend scenario with axios-like behavior"""
    print("\n=== TESTING FRONTEND SCENARIO ===")

    # Step 1: Get token like frontend does
    token_url = 'http://localhost:8000/api-token-auth/'
    login_data = {'username': 'admin', 'password': 'admin123'}

    try:
        print('Getting token...')
        token_response = requests.post(token_url, json=login_data)

        if token_response.status_code != 200:
            print(f'❌ Token request failed: {token_response.status_code}')
            print(f'Response: {token_response.text}')
            return

        token = token_response.json().get('token')
        print(f'✅ Token obtained: {token[:20]}...')

        # Step 2: Make delivery note request exactly like frontend
        delivery_url = 'http://localhost:8000/api/sales/delivery-notes/'
        delivery_data = {
            'sales_order': 20,
            'customer': 87,
            'delivery_date': '2025-07-14',
            'expected_delivery_date': '2025-07-15',
            'delivery_address': '17 Fallway road,Brampton',
            'delivery_contact_person': 'Farah Naz',
            'delivery_contact_phone': '03342217839',
            'vehicle_number': '',
            'driver_name': '',
            'driver_phone': '',
            'notes': '',
            'internal_notes': '',
            'status': 'draft'
        }

        # Headers exactly like axios would send
        headers = {
            'Authorization': f'Token {token}',
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Origin': 'http://localhost:5173',
            'Referer': 'http://localhost:5173/dashboard/sales/delivery-notes/create'
        }

        print('Creating delivery note...')
        delivery_response = requests.post(delivery_url, json=delivery_data, headers=headers)

        print(f'Status: {delivery_response.status_code}')
        print(f'Headers: {dict(delivery_response.headers)}')

        if delivery_response.status_code == 201:
            result = delivery_response.json()
            print(f'✅ Success! Created: {result.get("gdn_number")}')
        else:
            print(f'❌ Failed: {delivery_response.status_code}')
            print(f'Response: {delivery_response.text[:500]}')

    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_api_endpoint()
    test_frontend_scenario()
