import axios from 'axios';

// Use relative URL in development to leverage Vite proxy, absolute URL in production
const getBaseURL = () => {
  // If VITE_API_URL is explicitly set, use it
  if (import.meta.env.VITE_API_URL) {
    return import.meta.env.VITE_API_URL;
  }

  // In development, use relative URL to leverage Vite proxy
  if (import.meta.env.DEV) {
    return '/api';
  }

  // In production, use absolute URL
  return 'http://localhost:8000/api';
};

const api = axios.create({
  baseURL: getBaseURL(),
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    console.log('API Request:', {
      url: config.url,
      method: config.method,
      hasToken: !!token,
      tokenLength: token?.length || 0,
      baseURL: config.baseURL,
      fullURL: `${config.baseURL}${config.url}`
    });

    if (token) {
      config.headers.Authorization = `Token ${token}`;
    } else {
      console.warn('No authentication token found for API request');
    }

    // Ensure content type is set for POST/PUT requests
    if (['post', 'put', 'patch'].includes(config.method?.toLowerCase() || '')) {
      config.headers['Content-Type'] = 'application/json';
    }

    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for better error handling
api.interceptors.response.use(
  (response) => {
    console.log('API Response Success:', {
      url: response.config?.url,
      status: response.status,
      method: response.config?.method
    });
    return response;
  },
  (error) => {
    console.error('API Response Error:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      headers: error.response?.headers,
      message: error.message
    });

    // Handle 500 errors specifically
    if (error.response?.status === 500) {
      console.error('Server Error (500):', {
        url: error.config?.url,
        requestData: error.config?.data,
        responseData: error.response?.data
      });
      return Promise.reject(new Error('Server error occurred. Please try again or contact support.'));
    }

    if (error.response?.status === 401) {
      // Only redirect to login if not already on login page
      const isLoginPage = window.location.pathname === '/login';
      if (!isLoginPage) {
        localStorage.removeItem('token');
        window.location.href = '/login';
        return Promise.reject(new Error('Session expired. Please login again.'));
      }
      // If already on login page, just remove token without redirect
      localStorage.removeItem('token');
    }

    if (error.response?.status === 404) {
      console.warn(`API endpoint not found: ${error.config.url}`);
      // Return empty data for GET requests
      if (error.config.method?.toLowerCase() === 'get') {
        return { data: error.config.url.includes('/menu') ? [] : null };
      }
    }

    if (error.response?.data?.error) {
      return Promise.reject(new Error(error.response.data.error));
    }

    return Promise.reject(error);
  }
);

export default api;